[gd_resource type="Resource" script_class="PlayerBaseStatsData" load_steps=2 format=3 uid="uid://deedvv7l5unke"]

[ext_resource type="Script" uid="uid://jwln6lufvucu" path="res://src/player/player_base_stats_data.gd" id="1_7dshv"]

[resource]
script = ExtResource("1_7dshv")
max_health = 1
max_paint = 36
move_duration = 0.15
extra_lives = 0
size_modifier = 1.0
tile_size = 8
initial_repeat_delay = 0.15
repeat_rate = 0.1
paint_color = Color(1, 0.466667, 0.662745, 1)
metadata/_custom_type_script = "uid://jwln6lufvucu"
