[gd_resource type="Resource" script_class="ArtifactData" load_steps=5 format=3 uid="uid://bwbbxa0omp7fl"]

[ext_resource type="Script" uid="uid://d14cb5snb7hab" path="res://src/artifact/artifact_data.gd" id="1"]
[ext_resource type="Script" uid="uid://7rmrbgfrsmlf" path="res://src/ability/paint_streak_ability_data.gd" id="2"]
[ext_resource type="Texture2D" uid="uid://bxu67k15ktat" path="res://assets/eraser.png" id="3"]

[sub_resource type="Resource" id="PaintStreakData"]
script = ExtResource("2")
streak_threshold = 15

[resource]
script = ExtResource("1")
id = "paint_streak"
display_name = "Серия мастера"
description = "После 15 подряд закрашенных тайлов краска не тратится."
icon = ExtResource("3")
cost = 30
ability_data = SubResource("PaintStreakData")
