[gd_resource type="Resource" script_class="ArtifactData" load_steps=5 format=3 uid="uid://t3i4i1r8nux7"]

[ext_resource type="Script" path="res://src/ability/streak_bonus_ability_data.gd" id="1_nft1k"]
[ext_resource type="Texture2D" uid="uid://dunym6c7vtv8i" path="res://assets/cog_silver.png" id="2_1nktp"]
[ext_resource type="Script" uid="uid://d14cb5snb7hab" path="res://src/artifact/artifact_data.gd" id="3_9hwgi"]

[sub_resource type="Resource" id="StreakBonusData"]
script = ExtResource("1_nft1k")
streak_goal = 15

[resource]
script = ExtResource("3_9hwgi")
id = "streak_bonus"
display_name = "Серия вдохновения"
description = "Каждое 15-е закрашивание новой плитки бесплатно."
icon = ExtResource("2_1nktp")
cost = 35
ability_data = SubResource("StreakBonusData")
