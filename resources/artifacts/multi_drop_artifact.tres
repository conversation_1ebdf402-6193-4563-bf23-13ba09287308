[gd_resource type="Resource" script_class="ArtifactData" load_steps=5 format=3 uid="uid://c04fyhkiudgiq"]

[ext_resource type="Script" uid="uid://bkusl4o508on1" path="res://src/ability/multi_drop_ability_data.gd" id="1_w1ttq"]
[ext_resource type="Texture2D" uid="uid://cj35vjs6j4qr3" path="res://assets/banana.png" id="2_e1ajs"]
[ext_resource type="Script" uid="uid://d14cb5snb7hab" path="res://src/artifact/artifact_data.gd" id="2_lmrc5"]

[sub_resource type="Resource" id="MultiDropData"]
script = ExtResource("1_w1ttq")
drop_count = 3

[resource]
script = ExtResource("2_lmrc5")
id = "multi_drop"
display_name = "Мультикапля"
description = "Много капель."
icon = ExtResource("2_e1ajs")
cost = 25
ability_data = SubResource("MultiDropData")
