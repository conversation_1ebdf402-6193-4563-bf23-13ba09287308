[gd_resource type="Resource" script_class="ArtifactData" load_steps=5 format=3 uid="uid://bc8xkjdxbd56q"]

[ext_resource type="Script" uid="uid://d14cb5snb7hab" path="res://src/artifact/artifact_data.gd" id="1"]
[ext_resource type="Script" uid="uid://bki03j4s4yo60" path="res://src/ability/painted_teleport_ability_data.gd" id="2"]
[ext_resource type="Texture2D" uid="uid://bxu67k15ktat" path="res://assets/eraser.png" id="2_icon"]

[sub_resource type="Resource" id="PaintedTeleportData"]
script = ExtResource("2")
paint_cost = 2

[resource]
script = ExtResource("1")
id = "painted_teleport"
display_name = "Краска-портал"
description = "Телепортирует через стены и границы уровня по закрашенным тайлам."
icon = ExtResource("2_icon")
cost = 35
ability_data = SubResource("PaintedTeleportData")
