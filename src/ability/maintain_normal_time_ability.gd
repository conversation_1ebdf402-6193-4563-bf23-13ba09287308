class_name MaintainNormalTimeAbility
extends Ability

var _time_system: TimeDilationSystem
var _tile_query_system: TileQuerySystem
var _movement_component: MovementComponent

func initialize(p_owner_node: Node2D) -> void:
	owner_node = p_owner_node
	_time_system = _find_in_root(&"TimeDilationSystem")
	if not is_instance_valid(_time_system):
		push_warning("MaintainNormalTimeAbility: TimeDilationSystem not found")
		return
	_tile_query_system = _find_in_root(&"TileQuerySystem")
	_movement_component = _find_in_owner(&"MovementComponent")
	_time_system.speed_up_check.connect(_on_speed_up_check)

func _exit_tree() -> void:
	if not is_instance_valid(_time_system):
		return
	_time_system.speed_up_check.disconnect(_on_speed_up_check)

func _on_speed_up_check(context: SpeedUpCheckContext) -> void:
	if _tile_query_system == null or _movement_component == null:
		return
	var actor: Node2D = _movement_component.actor
	var parent_node: Node2D = actor.get_parent() as Node2D
	var global_target_pos: Vector2 = parent_node.to_global(_movement_component.target_position)
	var target_tile: Node2D = _tile_query_system.get_tile_at_global_pos(global_target_pos)
	if is_instance_valid(target_tile) and target_tile is ColorTile and (target_tile as ColorTile).is_painted():
		context.allow_speed_up = false
