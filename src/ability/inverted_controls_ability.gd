class_name InvertedControlsAbility
extends Ability

var remaining_time: float = 0.0
var _movement_component: MovementComponent
var _is_player_moving: bool = false

func initialize(p_owner_node: Node2D) -> void:
	owner_node = p_owner_node
	var ability_data := data as InvertedControlsAbilityData
	remaining_time = ability_data.duration
	_movement_component = _find_in_owner(&"MovementComponent")
	_movement_component.movement_started.connect(_on_movement_started)
	_movement_component.movement_completed.connect(_on_movement_completed)
	set_process(true)

func _exit_tree() -> void:
	_movement_component.movement_started.disconnect(_on_movement_started)
	_movement_component.movement_completed.disconnect(_on_movement_completed)
	set_process(false)

func _process(delta: float) -> void:
	var ability_data := data as InvertedControlsAbilityData
	var effective_delta: float = delta
	if _is_player_moving:
		effective_delta *= ability_data.time_scale_on_move
	remaining_time -= effective_delta
	if remaining_time <= 0.0:
		var comp := get_parent() as AbilityComponent
		if comp != null:
			comp.remove_ability(self)
		else:
			queue_free()

func _on_movement_started(_dir: Vector2) -> void:
	_is_player_moving = true

func _on_movement_completed() -> void:
	_is_player_moving = false
