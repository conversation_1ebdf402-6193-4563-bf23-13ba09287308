class_name PaintStreakAbility
extends Ability

var _current_streak: int = 0
var _is_active: bool = false
var _paint_system: PaintSystem

func initialize(p_owner_node: Node2D) -> void:
	owner_node = p_owner_node
	_paint_system = _find_in_root(&"PaintSystem")
	if is_instance_valid(_paint_system):
		_paint_system.new_tile_painted.connect(_on_new_tile_painted)
		_paint_system.player_landed_on_painted_tile.connect(_on_landed_on_painted_tile)
		_paint_system.paint_consumption_check.connect(_on_paint_consumption_check)

func _exit_tree() -> void:
	if is_instance_valid(_paint_system):
		_paint_system.new_tile_painted.disconnect(_on_new_tile_painted)
		_paint_system.player_landed_on_painted_tile.disconnect(_on_landed_on_painted_tile)
		_paint_system.paint_consumption_check.disconnect(_on_paint_consumption_check)

func _on_new_tile_painted(_tile: ColorTile) -> void:
	_current_streak += 1
	var streak_data := data as PaintStreakAbilityData
	if streak_data != null and _current_streak >= streak_data.streak_threshold:
		_is_active = true

func _on_landed_on_painted_tile(_paint_component: PaintComponent) -> void:
	_current_streak = 0
	_is_active = false

func _on_paint_consumption_check(context: PaintConsumptionContext) -> void:
	if _is_active:
		context.can_consume = false
