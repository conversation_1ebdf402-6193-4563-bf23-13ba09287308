class_name PaintedTeleportAbility
extends Ability

var _paint_component: PaintComponent
var _movement_component: MovementComponent
var _movement_system: MovementSystem
var _tile_query_system: TileQuerySystem
var _is_teleporting: bool = false

func initialize(p_owner_node: Node2D) -> void:
	owner_node = p_owner_node
	_movement_component = _find_in_owner(&"MovementComponent")
	_paint_component = _find_in_owner(&"PaintComponent")
	_movement_system = _find_in_root(&"MovementSystem")
	_tile_query_system = _find_in_root(&"TileQuerySystem")
	if is_instance_valid(_movement_component):
		_movement_component.movement_blocked.connect(_on_movement_blocked)

func _exit_tree() -> void:
	if is_instance_valid(_movement_component):
		_movement_component.movement_blocked.disconnect(_on_movement_blocked)

func _on_movement_blocked(direction: Vector2) -> void:
	var teleport_data := data as PaintedTeleportAbilityData
	if teleport_data == null or _is_teleporting:
		return
	if _paint_component == null or _paint_component.current_paint < teleport_data.paint_cost:
		return

	var current_tile: Node2D = _tile_query_system.get_tile_at_global_pos(owner_node.global_position)
	if not is_instance_valid(current_tile) or not (current_tile is ColorTile and (current_tile as ColorTile).is_painted()):
		return

	# Determine if the blocking wall has level behind it using TileQuerySystem
	var tile_size: int = _movement_component.data.tile_size
	var wall_pos: Vector2 = owner_node.global_position + direction * tile_size
	var tile_beyond_wall: Node2D = _tile_query_system.find_first_tile_in_direction(wall_pos, direction)

	var target_tile: Node2D
	if is_instance_valid(tile_beyond_wall):
		target_tile = _find_internal_teleport_target(direction)
	else:
		target_tile = _find_pacman_teleport_target(direction)

	if not is_instance_valid(target_tile):
		return

	_paint_component.current_paint -= teleport_data.paint_cost
	_is_teleporting = true
	_movement_system.teleport_to_position(_movement_component, target_tile.global_position)
	_movement_component.movement_completed.connect(func() -> void:
		_is_teleporting = false, CONNECT_ONE_SHOT)

func _find_internal_teleport_target(direction: Vector2) -> Node2D:
	if _movement_component == null:
		return null

	var step: int = _movement_component.data.tile_size
	var pos: Vector2 = owner_node.global_position + direction * (step * 2)
	var limit: int = 64

	while limit > 0:
		var tile: Node2D = _tile_query_system.get_tile_at_global_pos(pos)

		if not is_instance_valid(tile):
			return null

		if tile is ColorTile and not (tile as ColorTile).is_painted():
			return null

		if tile is ColorTile and (tile as ColorTile).is_painted():
			return tile

		pos += direction * step
		limit -= 1

	return null

func _find_pacman_teleport_target(direction: Vector2) -> Node2D:
	var wrapped_pos: Vector2 = _tile_query_system.get_wrapped_global_position(owner_node.global_position, direction)
	if wrapped_pos == Vector2.INF:
		return null

	var wrapped_tile: Node2D = _tile_query_system.get_tile_at_global_pos(wrapped_pos)
	if is_instance_valid(wrapped_tile) and wrapped_tile is ColorTile and (wrapped_tile as ColorTile).is_painted():
		return wrapped_tile

	return null
