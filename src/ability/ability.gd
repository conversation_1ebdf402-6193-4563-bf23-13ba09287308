class_name Ability
extends Node

@export var data: AbilityData
var owner_node: Node2D

func initialize(p_owner_node: Node2D) -> void:
	owner_node = p_owner_node


func _find_in_owner(node_name: StringName) -> Node:
	if owner_node == null:
		return null
	return owner_node.find_child(node_name, true, false)

func _find_in_root(node_name: StringName) -> Node:
	return get_tree().root.find_child(node_name, true, false)
