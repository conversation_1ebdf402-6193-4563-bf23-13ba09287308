class_name RestorePaintAbility
extends Ability

var _paint_system: PaintSystem

func initialize(p_owner_node: Node2D) -> void:
	owner_node = p_owner_node
	_paint_system = _find_in_root(&"PaintSystem")
	if is_instance_valid(_paint_system):
		_paint_system.player_landed_on_painted_tile.connect(_on_player_landed_on_painted_tile)

func _exit_tree() -> void:
	if is_instance_valid(_paint_system):
		_paint_system.player_landed_on_painted_tile.disconnect(_on_player_landed_on_painted_tile)

func _on_player_landed_on_painted_tile(paint_component: PaintComponent) -> void:
	var ability_data := data as RestorePaintAbilityData
	if ability_data == null:
		return
	paint_component.current_paint += ability_data.paint_to_restore
