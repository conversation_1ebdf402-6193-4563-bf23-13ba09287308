class_name ArtifactsDisplay
extends Control

@export var player_service: PlayerService
@export var container: HBoxContainer
@export var artifact_icon_scene: PackedScene

var _current_inventory: ArtifactInventoryComponent

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	var inventory: ArtifactInventoryComponent = player_service.get_artifact_inventory_component()
	if not is_instance_valid(inventory):
		return
	if is_instance_valid(_current_inventory):
		if _current_inventory.artifact_added.is_connected(_on_artifact_added):
			_current_inventory.artifact_added.disconnect(_on_artifact_added)
	_current_inventory = inventory
	_current_inventory.artifact_added.connect(_on_artifact_added)
	_redraw_all_artifacts()

func _redraw_all_artifacts() -> void:
	for child in container.get_children():
		child.queue_free()
	if not is_instance_valid(_current_inventory):
		return
	for data in _current_inventory.get_artifacts():
		_add_artifact_icon(data)

func _on_artifact_added(data: ArtifactData) -> void:
	_add_artifact_icon(data)

func _add_artifact_icon(data: ArtifactData) -> void:
	var artifact_icon: ArtifactIcon = artifact_icon_scene.instantiate() as ArtifactIcon
	container.add_child(artifact_icon)
	artifact_icon.set_artifact(data)
