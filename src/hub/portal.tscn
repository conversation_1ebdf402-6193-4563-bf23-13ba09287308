[gd_scene load_steps=4 format=3 uid="uid://c8lam3n4p5q6r"]

[ext_resource type="Script" uid="uid://8bxu26fng2a6" path="res://src/hub/portal.gd" id="1_portal"]
[ext_resource type="Texture2D" uid="uid://dxvmoabu0ee3q" path="res://assets/portal.png" id="2_eat10"]

[sub_resource type="CircleShape2D" id="CircleShape2D_eat10"]
radius = 2.0

[node name="Portal" type="Area2D"]
script = ExtResource("1_portal")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_eat10")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.5, 0.8, 1, 0.8)
texture_filter = 1
texture = ExtResource("2_eat10")
