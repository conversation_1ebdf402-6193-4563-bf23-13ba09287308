[gd_scene load_steps=17 format=4 uid="uid://bqx8h4n5m2k7p"]

[ext_resource type="Script" uid="uid://bxnlwxu5qs7lf" path="res://src/hub/hub.gd" id="1_hub"]
[ext_resource type="PackedScene" uid="uid://c8lam3n4p5q6r" path="res://src/hub/portal.tscn" id="3_portal"]
[ext_resource type="PackedScene" uid="uid://2o2nqedmdng0" path="res://src/color_tile/color_tile.tscn" id="4_lgcv4"]
[ext_resource type="PackedScene" uid="uid://dfy35tmjoeoht" path="res://src/water_tile/water_tile.tscn" id="5_gb5ag"]
[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="6_0pbk3"]
[ext_resource type="PackedScene" uid="uid://dmr0fcamx7t56" path="res://addons/virtual_joystick/virtual_joystick_scene.tscn" id="11_wj811"]
[ext_resource type="Script" uid="uid://cndyjg0c5r3k4" path="res://src/player/player_service.gd" id="18_df2jy"]
[ext_resource type="PackedScene" uid="uid://biw4o1x1u6l4i" path="res://src/tile_query_system/tile_query_system.tscn" id="18_mmg22"]
[ext_resource type="PackedScene" uid="uid://bi0dhs7jf7yrl" path="res://src/movement/movement_system.tscn" id="19_m8kwa"]
[ext_resource type="PackedScene" uid="uid://5agsxx7ekdi6" path="res://src/player/player_input_system.tscn" id="20_wlut3"]
[ext_resource type="PackedScene" uid="uid://d1sjx836adi6t" path="res://src/hub/upgrade_ui.tscn" id="21_upgrade_ui"]
[ext_resource type="PackedScene" uid="uid://bjcku4dtsyta2" path="res://src/hub/upgrade_npc.tscn" id="22_upgrade_npc"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_v8286"]
scenes/1/scene = ExtResource("4_lgcv4")
scenes/2/scene = ExtResource("5_gb5ag")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_2f7ys"]
texture = ExtResource("6_0pbk3")
texture_region_size = Vector2i(8, 8)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:0/0 = 0
0:1/0 = 0
0:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
1:1/0 = 0
1:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
2:1/0 = 0
2:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:1/0 = 0
3:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)

[sub_resource type="TileSet" id="TileSet_0pbk3"]
tile_size = Vector2i(8, 8)
physics_layer_0/collision_layer = 1
physics_layer_1/collision_layer = 2
physics_layer_1/collision_mask = 0
sources/1 = SubResource("TileSetAtlasSource_2f7ys")
sources/0 = SubResource("TileSetScenesCollectionSource_v8286")

[sub_resource type="CircleShape2D" id="CircleShape2D_v8286"]
radius = 1.05

[node name="Hub" type="Node2D" node_paths=PackedStringArray("portal", "tile_query_system", "player_service")]
scale = Vector2(8, 8)
script = ExtResource("1_hub")
portal = NodePath("Portal")
tile_query_system = NodePath("Systems/TileQuerySystem")
player_service = NodePath("Services/PlayerService")

[node name="TileMapLayer" type="TileMapLayer" parent="."]
texture_filter = 1
position = Vector2(0, -0.875)
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_0pbk3")

[node name="Portal" parent="." instance=ExtResource("3_portal")]
position = Vector2(59.875, 114)

[node name="UpgradeNpc" parent="." node_paths=PackedStringArray("upgrade_ui") instance=ExtResource("22_upgrade_npc")]
position = Vector2(20, 74.375)
upgrade_ui = NodePath("../CanvasLayer/UpgradeUI")

[node name="CollisionShape2D" type="CollisionShape2D" parent="UpgradeNpc"]
position = Vector2(7.75, 0.625)
shape = SubResource("CircleShape2D_v8286")

[node name="Services" type="Node" parent="."]

[node name="PlayerService" type="Node" parent="Services"]
script = ExtResource("18_df2jy")
metadata/_custom_type_script = "uid://cndyjg0c5r3k4"

[node name="Systems" type="Node" parent="."]

[node name="TileQuerySystem" parent="Systems" instance=ExtResource("18_mmg22")]

[node name="MovementSystem" parent="Systems" node_paths=PackedStringArray("player_service", "tile_query_system") instance=ExtResource("19_m8kwa")]
player_service = NodePath("../../Services/PlayerService")
tile_query_system = NodePath("../TileQuerySystem")

[node name="PlayerInputSystem" parent="Systems" node_paths=PackedStringArray("movement_system", "player_service") instance=ExtResource("20_wlut3")]
movement_system = NodePath("../MovementSystem")
player_service = NodePath("../../Services/PlayerService")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="UpgradeUI" parent="CanvasLayer" instance=ExtResource("21_upgrade_ui")]

[node name="Virtual Joystick" parent="CanvasLayer" instance=ExtResource("11_wj811")]
anchors_preset = 15
anchor_top = 0.0
anchor_right = 1.0
offset_top = 0.0
offset_right = 0.0
offset_bottom = 0.0
grow_horizontal = 2
grow_vertical = 2
deadzone_size = 40.0
joystick_mode = 2
visibility_mode = 3
action_left = "move_left"
action_right = "move_right"
action_up = "move_up"
action_down = "move_down"

[editable path="CanvasLayer/Virtual Joystick"]
