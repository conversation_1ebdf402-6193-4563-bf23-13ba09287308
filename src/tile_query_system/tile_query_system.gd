class_name TileQuerySystem
extends Node

var _tile_map: Dictionary = {}
var _rows: Dictionary = {} # Упрощенные названия
var _columns: Dictionary = {}

func get_tile_at_global_pos(position: Vector2) -> Node2D:
	return _tile_map.get(position, null)

func get_nearest_tile(position: Vector2) -> Node2D:
	if _tile_map.is_empty():
		return null

	var nearest_tile: Node2D = null
	var min_distance: float = INF

	for tile_pos: Vector2 in _tile_map:
		var distance: float = position.distance_squared_to(tile_pos)
		if distance < min_distance:
			min_distance = distance
			nearest_tile = _tile_map[tile_pos]

	return nearest_tile

func get_random_tile() -> Node2D:
	if _tile_map.is_empty():
		return null

	var tiles_array := _tile_map.values()
	return tiles_array.pick_random()

func build_map() -> void:
	# Очищаем предыдущие данные
	_tile_map.clear()
	_rows.clear()
	_columns.clear()

	# Собираем все тайлы
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"tiles")

	for tile_node: Node2D in tiles:
		var pos: Vector2 = tile_node.global_position
		_tile_map[pos] = tile_node

		# Группируем по строкам и столбцам (округляем для группировки)
		var row: int = roundi(pos.y)
		var col: int = roundi(pos.x)

		# Добавляем в соответствующую строку
		if not _rows.has(row):
			_rows[row] = []
		var row_array: Array = _rows[row]
		row_array.append(pos)

		# Добавляем в соответствующий столбец
		if not _columns.has(col):
			_columns[col] = []
		var col_array: Array = _columns[col]
		col_array.append(pos)

	# Сортируем позиции в строках и столбцах
	for row: int in _rows:
		var row_array: Array = _rows[row]
		row_array.sort_custom(func(a: Vector2, b: Vector2) -> bool: return a.x < b.x)

	for col: int in _columns:
		var col_array: Array = _columns[col]
		col_array.sort_custom(func(a: Vector2, b: Vector2) -> bool: return a.y < b.y)

func get_wrapped_global_position(current_pos: Vector2, direction: Vector2) -> Vector2:
	if _tile_map.is_empty():
		return Vector2.INF

	# Находим текущий тайл
	var current_tile: Node2D = get_tile_at_global_pos(current_pos)
	if not current_tile:
		current_tile = get_nearest_tile(current_pos)
		if not current_tile:
			return Vector2.INF

	var tile_pos: Vector2 = current_tile.global_position

	# Определяем направление движения и ищем тайл для телепортации
	if not is_zero_approx(direction.x):
		# Движение по горизонтали
		return _find_wrap_position_horizontal(tile_pos, direction.x > 0)
	elif not is_zero_approx(direction.y):
		# Движение по вертикали
		return _find_wrap_position_vertical(tile_pos, direction.y > 0)

	return Vector2.INF

func _find_wrap_position_horizontal(current_pos: Vector2, moving_right: bool) -> Vector2:
	var row: int = roundi(current_pos.y)
	var tiles_in_row: Array = _rows.get(row, [])

	if tiles_in_row.is_empty():
		return Vector2.INF

	# Ищем крайний ColorTile в противоположной стороне
	var tiles_to_check: Array = tiles_in_row.duplicate()
	if not moving_right:
		tiles_to_check.reverse()

	return _find_first_color_tile(tiles_to_check, current_pos)

func _find_wrap_position_vertical(current_pos: Vector2, moving_down: bool) -> Vector2:
	var col: int = roundi(current_pos.x)
	var tiles_in_column: Array = _columns.get(col, [])

	if tiles_in_column.is_empty():
		return Vector2.INF

	# Ищем крайний ColorTile в противоположной стороне
	var tiles_to_check: Array = tiles_in_column.duplicate()
	if not moving_down:
		tiles_to_check.reverse()

	return _find_first_color_tile(tiles_to_check, current_pos)

func _find_first_color_tile(tiles: Array, current_pos: Vector2) -> Vector2:
	for tile_pos: Vector2 in tiles:
		# Пропускаем текущий тайл
		if tile_pos.is_equal_approx(current_pos):
			continue

		# Проверяем, что это ColorTile
		var tile: Node2D = _tile_map.get(tile_pos)
		if tile is ColorTile:
			return tile_pos

	return Vector2.INF
