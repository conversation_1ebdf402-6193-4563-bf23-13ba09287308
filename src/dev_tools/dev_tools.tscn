[gd_scene load_steps=2 format=3 uid="uid://cnma8j0xdww5"]

[ext_resource type="Script" uid="uid://dg1bgp1q16npr" path="res://src/dev_tools/dev_tools.gd" id="1_nwngj"]

[node name="DevTools" type="Node2D" node_paths=PackedStringArray("paint_all_tiles_button", "toggle_safe_zone_button", "add_inverted_controls_button", "reset_progress_button", "add_artifact_button")]
script = ExtResource("1_nwngj")
paint_all_tiles_button = NodePath("VBoxContainer/PaintAll")
toggle_safe_zone_button = NodePath("VBoxContainer/ToggleSafeZoneButton")
add_inverted_controls_button = NodePath("VBoxContainer/AddInvertedControlsButton")
reset_progress_button = NodePath("VBoxContainer/ResetProgressButton")
add_artifact_button = NodePath("VBoxContainer/AddArtifact")

[node name="VBoxContainer" type="HBoxContainer" parent="."]
offset_right = 222.0
offset_bottom = 66.0
theme_override_constants/separation = 0

[node name="PaintAll" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "PaintAll"

[node name="ToggleSafeZoneButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "SafeZones"

[node name="AddInvertedControlsButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "ApplyInvCntrls"

[node name="ResetProgressButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "ResetProgress"

[node name="AddArtifact" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "AddItem"
