class_name ShopItemPanel
extends Control

signal purchase_requested(artifact: ArtifactData)

@export var icon_rect: TextureRect
@export var name_label: Label
@export var cost_label: Label
@export var buy_button: Button

var _artifact: ArtifactData
var _is_purchased := false

func _ready() -> void:
	buy_button.pressed.connect(_on_buy_pressed)

func setup(artifact: ArtifactData) -> void:
	_artifact = artifact
	_is_purchased = false
	icon_rect.texture = artifact.icon
	name_label.text = artifact.display_name
	cost_label.text = "Стоимость: " + str(artifact.cost)
	_update_button()

func _update_button() -> void:
	if _is_purchased:
		buy_button.disabled = true
		buy_button.text = "Куплено"
		return
	var can_afford: bool = RunStateService.get_total_painted_tiles() >= (_artifact.cost if _artifact else 0)
	buy_button.disabled = not can_afford
	buy_button.text = ("Купить" if can_afford else "Недостаточно")

func _on_buy_pressed() -> void:
	if _artifact != null:
		purchase_requested.emit(_artifact)

func get_artifact() -> ArtifactData:
	return _artifact

func mark_purchased() -> void:
	_is_purchased = true
	_update_button()
