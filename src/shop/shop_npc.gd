class_name ShopNpc
extends Area2D

@export var shop_ui: ShopUI
@export var artifact_pool: ArtifactPool

func _ready() -> void:
	body_entered.connect(_on_body_entered)
	body_exited.connect(_on_body_exited)

func _on_body_entered(body: Node2D) -> void:
	if body.is_in_group(&"player"):
		var artifacts: Array[ArtifactData] = ArtifactRegistry.get_random_unowned_from_pool(artifact_pool, 3)
		shop_ui.show_ui(artifacts)

func _on_body_exited(body: Node2D) -> void:
	if body.is_in_group(&"player"):
		shop_ui.hide_ui()
