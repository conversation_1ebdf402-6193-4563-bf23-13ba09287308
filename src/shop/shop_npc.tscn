[gd_scene load_steps=4 format=3 uid="uid://d4jdklgcrhr5l"]

[ext_resource type="Script" uid="uid://brmfdfasxg6fw" path="res://src/shop/shop_npc.gd" id="1_shop_npc"]
[ext_resource type="Texture2D" uid="uid://bxu67k15ktat" path="res://assets/eraser.png" id="2_vx4m3"]

[sub_resource type="CircleShape2D" id="1_shape"]
radius = 3.0

[node name="ShopNpc" type="Area2D"]
collision_layer = 0
script = ExtResource("1_shop_npc")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("2_vx4m3")

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
shape = SubResource("1_shape")
