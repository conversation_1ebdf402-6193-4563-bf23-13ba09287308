[gd_scene load_steps=3 format=3 uid="uid://dv4hnxw5noomb"]

[ext_resource type="Script" uid="uid://dkogk3c7e4mnp" path="res://src/shop/shop_item_panel.gd" id="1"]
[ext_resource type="Texture2D" uid="uid://biagw5gvp674n" path="res://assets/color_drop.png" id="2_aan6m"]

[node name="ShopItemPanel" type="HBoxContainer" node_paths=PackedStringArray("icon_rect", "name_label", "cost_label", "buy_button")]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
alignment = 1
script = ExtResource("1")
icon_rect = NodePath("Icon")
name_label = NodePath("Name")
cost_label = NodePath("Cost")
buy_button = NodePath("BuyButton")

[node name="Icon" type="TextureRect" parent="."]
texture_filter = 1
custom_minimum_size = Vector2(150, 150)
layout_mode = 2
size_flags_horizontal = 3
texture = ExtResource("2_aan6m")
stretch_mode = 5

[node name="Name" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "Капля"

[node name="Cost" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "150"

[node name="BuyButton" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "Купить"
