[gd_scene load_steps=3 format=3 uid="uid://bg26gm6hjnwbj"]

[ext_resource type="Script" uid="uid://15qk2wrcolpo" path="res://src/shop/shop_ui.gd" id="1_shop_ui"]
[ext_resource type="PackedScene" uid="uid://dv4hnxw5noomb" path="res://src/shop/shop_item_panel.tscn" id="2_item_panel"]

[node name="ShopUI" type="Control" node_paths=PackedStringArray("currency_label", "items_container", "close_button")]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_shop_ui")
currency_label = NodePath("Panel/VBox/CurrencyLabel")
items_container = NodePath("Panel/VBox/Items")
item_panel_scene = ExtResource("2_item_panel")
close_button = NodePath("Panel/VBox/CloseButton")

[node name="Panel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBox" type="VBoxContainer" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="CurrencyLabel" type="Label" parent="Panel/VBox"]
layout_mode = 2
text = "Валюта: 0"
horizontal_alignment = 1

[node name="Items" type="VBoxContainer" parent="Panel/VBox"]
layout_mode = 2

[node name="ShopItemPanel" parent="Panel/VBox/Items" instance=ExtResource("2_item_panel")]
layout_mode = 2

[node name="ShopItemPanel2" parent="Panel/VBox/Items" instance=ExtResource("2_item_panel")]
layout_mode = 2

[node name="ShopItemPanel3" parent="Panel/VBox/Items" instance=ExtResource("2_item_panel")]
layout_mode = 2

[node name="CloseButton" type="Button" parent="Panel/VBox"]
layout_mode = 2
text = "Закрыть"
