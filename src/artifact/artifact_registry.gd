extends Node

func get_random_unowned_from_pool(pool: ArtifactPool, count: int) -> Array[ArtifactData]:
	if pool == null or pool.artifacts.is_empty():
		return []

	var unowned: Array[ArtifactData] = []
	for artifact in pool.artifacts:
		if not RunStateService.has_artifact(artifact.id):
			unowned.append(artifact)

	unowned.shuffle()
	if count >= unowned.size():
		return unowned
	return unowned.slice(0, count)
