class_name ArtifactInventoryComponent
extends Node

signal artifact_added(artifact_data: ArtifactData)

@export var ability_component: AbilityComponent

var _artifacts: Array[ArtifactData] = []

func add_artifact(artifact_data: ArtifactData, should_register: bool = true) -> void:
	if not has_artifact(artifact_data.id):
		_artifacts.append(artifact_data)
		if artifact_data.ability_data:
			ability_component.add_ability_from_data(artifact_data.ability_data)
		if should_register:
			RunStateService.add_artifact(artifact_data)
		artifact_added.emit(artifact_data)

func has_artifact(artifact_id: StringName) -> bool:
	for artifact in _artifacts:
		if artifact.id == artifact_id:
			return true
	return false

func get_artifacts() -> Array[ArtifactData]:
	return _artifacts
